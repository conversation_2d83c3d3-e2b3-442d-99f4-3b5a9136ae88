'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { User, Briefcase, Code, Mail, Home, Award } from 'lucide-react';
import { cn } from '@/lib/utils';

interface NavigationStar {
  id: string;
  label: string;
  icon: React.ComponentType<any>;
  position: { x: number; y: number };
  size: 'small' | 'medium' | 'large';
  color: string;
  description: string;
}

interface StarFieldNavigationProps {
  onNavigate: (sectionId: string) => void;
  currentSection?: string;
  className?: string;
}

// Desktop positions - repositioned to edges and corners to avoid content overlap
const desktopStars: NavigationStar[] = [
  {
    id: 'home',
    label: 'Home',
    icon: Home,
    position: { x: 10, y: 10 }, // Top-left corner
    size: 'medium',
    color: 'from-blue-400 to-cyan-400',
    description: 'Return to overview',
  },
  {
    id: 'about',
    label: 'About',
    icon: User,
    position: { x: 90, y: 15 }, // Top-right area
    size: 'large',
    color: 'from-purple-400 to-pink-400',
    description: 'Learn about my journey',
  },
  {
    id: 'projects',
    label: 'Projects',
    icon: Briefcase,
    position: { x: 95, y: 50 }, // Right edge center
    size: 'large',
    color: 'from-green-400 to-emerald-400',
    description: 'Explore my work',
  },
  {
    id: 'skills',
    label: 'Skills',
    icon: Code,
    position: { x: 90, y: 85 }, // Bottom-right area
    size: 'medium',
    color: 'from-orange-400 to-red-400',
    description: 'Technical expertise',
  },
  {
    id: 'experience',
    label: 'Experience',
    icon: Award,
    position: { x: 10, y: 85 }, // Bottom-left area
    size: 'medium',
    color: 'from-yellow-400 to-orange-400',
    description: 'Professional background',
  },
  {
    id: 'contact',
    label: 'Contact',
    icon: Mail,
    position: { x: 5, y: 50 }, // Left edge center
    size: 'small',
    color: 'from-indigo-400 to-purple-400',
    description: 'Get in touch',
  },
];

// Mobile positions - positioned at edges for better accessibility and content clearance
const mobileStars: NavigationStar[] = [
  {
    id: 'home',
    label: 'Home',
    icon: Home,
    position: { x: 15, y: 10 }, // Top-left
    size: 'large',
    color: 'from-blue-400 to-cyan-400',
    description: 'Return to overview',
  },
  {
    id: 'about',
    label: 'About',
    icon: User,
    position: { x: 85, y: 15 }, // Top-right
    size: 'large',
    color: 'from-purple-400 to-pink-400',
    description: 'Learn about my journey',
  },
  {
    id: 'projects',
    label: 'Projects',
    icon: Briefcase,
    position: { x: 90, y: 45 }, // Right edge
    size: 'large',
    color: 'from-green-400 to-emerald-400',
    description: 'Explore my work',
  },
  {
    id: 'skills',
    label: 'Skills',
    icon: Code,
    position: { x: 85, y: 80 }, // Bottom-right
    size: 'large',
    color: 'from-orange-400 to-red-400',
    description: 'Technical expertise',
  },
  {
    id: 'experience',
    label: 'Experience',
    icon: Award,
    position: { x: 15, y: 80 }, // Bottom-left
    size: 'large',
    color: 'from-yellow-400 to-orange-400',
    description: 'Professional background',
  },
  {
    id: 'contact',
    label: 'Contact',
    icon: Mail,
    position: { x: 10, y: 45 }, // Left edge
    size: 'large',
    color: 'from-indigo-400 to-purple-400',
    description: 'Get in touch',
  },
];

export const StarFieldNavigation: React.FC<StarFieldNavigationProps> = ({
  onNavigate,
  currentSection = 'home',
  className = '',
}) => {
  const [hoveredStar, setHoveredStar] = useState<string | null>(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isMobile, setIsMobile] = useState(false);
  const [focusedStarIndex, setFocusedStarIndex] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);

  // Detect mobile screen size
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const navigationStars = isMobile ? mobileStars : desktopStars;

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        setMousePosition({
          x: e.clientX - rect.left,
          y: e.clientY - rect.top,
        });
      }
    };

    const container = containerRef.current;
    if (container) {
      container.addEventListener('mousemove', handleMouseMove);
      return () => container.removeEventListener('mousemove', handleMouseMove);
    }
  }, []);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Tab') {
        e.preventDefault();
        setFocusedStarIndex((prev) => (prev + 1) % navigationStars.length);
      } else if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        onNavigate(navigationStars[focusedStarIndex].id);
      } else if (e.key === 'ArrowUp' || e.key === 'ArrowDown' || e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
        e.preventDefault();
        setFocusedStarIndex((prev) => {
          if (e.key === 'ArrowUp' || e.key === 'ArrowLeft') {
            return prev > 0 ? prev - 1 : navigationStars.length - 1;
          } else {
            return (prev + 1) % navigationStars.length;
          }
        });
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [focusedStarIndex, navigationStars, onNavigate]);

  const getSizeClasses = (size: string, isActive: boolean, isHovered: boolean) => {
    if (isMobile) {
      // Mobile sizes - larger and more touch-friendly
      const baseSize = isActive ? 'w-16 h-16' : 'w-14 h-14';
      return `${baseSize} active:w-18 active:h-18`;
    }

    const baseSize = {
      small: isActive ? 'w-8 h-8' : 'w-6 h-6',
      medium: isActive ? 'w-12 h-12' : 'w-10 h-10',
      large: isActive ? 'w-16 h-16' : 'w-14 h-14',
    }[size];

    const hoverSize = {
      small: 'hover:w-10 hover:h-10',
      medium: 'hover:w-14 hover:h-14',
      large: 'hover:w-18 hover:h-18',
    }[size];

    return `${baseSize} ${hoverSize}`;
  };

  const getIconSize = (size: string) => {
    if (isMobile) {
      return 'w-6 h-6'; // Larger icons for mobile
    }

    return {
      small: 'w-3 h-3',
      medium: 'w-4 h-4',
      large: 'w-5 h-5',
    }[size];
  };

  return (
    <div
      ref={containerRef}
      className={cn(
        'fixed inset-0 z-40 pointer-events-none',
        'bg-gradient-to-br from-slate-900 via-purple-900/20 to-slate-900',
        className
      )}
    >
      {/* Background stars */}
      <div className="absolute inset-0">
        {[...Array(100)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-px h-px bg-white rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              opacity: [0.2, 0.8, 0.2],
              scale: [1, 1.5, 1],
            }}
            transition={{
              duration: 2 + Math.random() * 3,
              repeat: Infinity,
              delay: Math.random() * 2,
              ease: 'easeInOut',
            }}
          />
        ))}
      </div>

      {/* Navigation Stars */}
      {navigationStars.map((star, index) => {
        const Icon = star.icon;
        const isActive = currentSection === star.id;
        const isHovered = hoveredStar === star.id;
        const isFocused = focusedStarIndex === index;

        return (
          <motion.div
            key={star.id}
            className="absolute pointer-events-auto"
            style={{
              left: `${star.position.x}%`,
              top: `${star.position.y}%`,
              transform: 'translate(-50%, -50%)',
            }}
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{
              duration: 0.6,
              delay: navigationStars.indexOf(star) * 0.1,
              ease: 'easeOut',
            }}
          >
            {/* Star glow effect */}
            <motion.div
              className={cn(
                'absolute inset-0 rounded-full blur-xl',
                `bg-gradient-to-r ${star.color}`,
                isActive ? 'opacity-60' : 'opacity-30'
              )}
              animate={{
                scale: isHovered || isFocused ? 1.5 : isActive ? 1.2 : 1,
                opacity: isHovered || isFocused ? 0.8 : isActive ? 0.6 : 0.3,
              }}
              transition={{ duration: 0.3 }}
            />

            {/* Main star */}
            <motion.button
              className={cn(
                'relative rounded-full border-2 border-white/20',
                'backdrop-blur-sm transition-all duration-300',
                'flex items-center justify-center group',
                `bg-gradient-to-r ${star.color}`,
                getSizeClasses(star.size, isActive, isHovered),
                isActive ? 'shadow-2xl' : 'shadow-lg',
                'hover:border-white/40 hover:shadow-2xl',
                'focus:outline-none focus:ring-4 focus:ring-white/30',
                isFocused ? 'ring-4 ring-white/30' : ''
              )}
              onClick={() => onNavigate(star.id)}
              onMouseEnter={() => setHoveredStar(star.id)}
              onMouseLeave={() => setHoveredStar(null)}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
              aria-label={`Navigate to ${star.label} section - ${star.description}`}
              role="button"
              tabIndex={isFocused ? 0 : -1}
            >
              <Icon className={cn(
                'text-white drop-shadow-lg',
                getIconSize(star.size)
              )} />

              {/* Pulse effect for active star */}
              {isActive && (
                <motion.div
                  className={cn(
                    'absolute inset-0 rounded-full border-2 border-white/40',
                    `bg-gradient-to-r ${star.color}`
                  )}
                  animate={{
                    scale: [1, 1.5, 1],
                    opacity: [0.5, 0, 0.5],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: 'easeInOut',
                  }}
                />
              )}
            </motion.button>

            {/* Label */}
            <AnimatePresence>
              {(isHovered || isActive || isFocused) && (
                <motion.div
                  initial={{ opacity: 0, y: 10, scale: 0.8 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  exit={{ opacity: 0, y: 10, scale: 0.8 }}
                  transition={{ duration: 0.2 }}
                  className="absolute top-full mt-3 left-1/2 transform -translate-x-1/2 pointer-events-none z-50"
                >
                  <div className="bg-black/80 backdrop-blur-sm border border-white/20 rounded-lg px-3 py-2 text-center">
                    <p className="text-white font-medium text-sm whitespace-nowrap">
                      {star.label}
                    </p>
                    <p className="text-white/70 text-xs mt-1 whitespace-nowrap">
                      {star.description}
                    </p>
                  </div>
                  {/* Arrow pointing to star */}
                  <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-white/20" />
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        );
      })}

      {/* Constellation lines */}
      <svg className="absolute inset-0 w-full h-full pointer-events-none">
        {navigationStars.map((star, index) => {
          if (index === navigationStars.length - 1) return null;
          
          const nextStar = navigationStars[index + 1];
          const isConnected = currentSection === star.id || currentSection === nextStar.id;
          
          return (
            <motion.line
              key={`${star.id}-${nextStar.id}`}
              x1={`${star.position.x}%`}
              y1={`${star.position.y}%`}
              x2={`${nextStar.position.x}%`}
              y2={`${nextStar.position.y}%`}
              stroke="rgba(255, 255, 255, 0.1)"
              strokeWidth="1"
              strokeDasharray="2,4"
              initial={{ pathLength: 0, opacity: 0 }}
              animate={{ 
                pathLength: isConnected ? 1 : 0.3,
                opacity: isConnected ? 0.4 : 0.1
              }}
              transition={{ duration: 0.8, ease: 'easeInOut' }}
            />
          );
        })}
      </svg>

      {/* Instructions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 1 }}
        className="absolute bottom-4 sm:bottom-8 left-1/2 transform -translate-x-1/2 text-center pointer-events-none px-4"
      >
        <p className="text-white/60 text-xs sm:text-sm">
          {isMobile ? 'Tap on the stars to navigate' : 'Click on the stars to navigate through the portfolio'}
        </p>
      </motion.div>
    </div>
  );
};
